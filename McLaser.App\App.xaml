<Application x:Class="McLaser.App.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Startup="Application_Startup"
             Exit="Application_Exit">
    <Application.Resources>
        <!-- 应用程序级别的资源 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 默认样式 -->
                <ResourceDictionary>
                    <!-- 默认窗口样式 -->
                    <Style TargetType="Window">
                        <Setter Property="FontFamily" value="Microsoft YaHei UI" />
                        <Setter Property="FontSize" value="12" />
                        <Setter Property="Background" value="{DynamicResource WindowBackgroundBrush}" />
                        <Setter Property="Foreground" value="{DynamicResource WindowForegroundBrush}" />
                    </Style>
                    
                    <!-- 默认按钮样式 -->
                    <Style TargetType="Button">
                        <Setter Property="Padding" value="8,4" />
                        <Setter Property="Margin" value="4" />
                        <Setter Property="MinWidth" value="75" />
                        <Setter Property="Background" value="{DynamicResource ButtonBackgroundBrush}" />
                        <Setter Property="Foreground" value="{DynamicResource ButtonForegroundBrush}" />
                        <Setter Property="BorderBrush" value="{DynamicResource ButtonBorderBrush}" />
                        <Setter Property="BorderThickness" value="1" />
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" value="{DynamicResource ButtonHoverBackgroundBrush}" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" value="{DynamicResource ButtonPressedBackgroundBrush}" />
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                    
                    <!-- 默认文本框样式 -->
                    <Style TargetType="TextBox">
                        <Setter Property="Padding" value="4" />
                        <Setter Property="Margin" value="4" />
                        <Setter Property="Background" value="{DynamicResource TextBoxBackgroundBrush}" />
                        <Setter Property="Foreground" value="{DynamicResource TextBoxForegroundBrush}" />
                        <Setter Property="BorderBrush" value="{DynamicResource TextBoxBorderBrush}" />
                        <Setter Property="BorderThickness" value="1" />
                    </Style>
                    
                    <!-- 默认标签样式 -->
                    <Style TargetType="Label">
                        <Setter Property="Foreground" value="{DynamicResource LabelForegroundBrush}" />
                    </Style>
                    
                    <!-- 默认菜单样式 -->
                    <Style TargetType="Menu">
                        <Setter Property="Background" value="{DynamicResource MenuBackgroundBrush}" />
                        <Setter Property="Foreground" value="{DynamicResource MenuForegroundBrush}" />
                    </Style>
                    
                    <!-- 默认工具栏样式 -->
                    <Style TargetType="ToolBar">
                        <Setter Property="Background" value="{DynamicResource ToolBarBackgroundBrush}" />
                        <Setter Property="Foreground" value="{DynamicResource ToolBarForegroundBrush}" />
                    </Style>
                    
                    <!-- 默认状态栏样式 -->
                    <Style TargetType="StatusBar">
                        <Setter Property="Background" value="{DynamicResource StatusBarBackgroundBrush}" />
                        <Setter Property="Foreground" value="{DynamicResource StatusBarForegroundBrush}" />
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 默认颜色资源 -->
            <SolidColorBrush x:Key="WindowBackgroundBrush" Color="White" />
            <SolidColorBrush x:Key="WindowForegroundBrush" Color="Black" />
            
            <SolidColorBrush x:Key="ButtonBackgroundBrush" Color="#F0F0F0" />
            <SolidColorBrush x:Key="ButtonForegroundBrush" Color="Black" />
            <SolidColorBrush x:Key="ButtonBorderBrush" Color="#CCCCCC" />
            <SolidColorBrush x:Key="ButtonHoverBackgroundBrush" Color="#E0E0E0" />
            <SolidColorBrush x:Key="ButtonPressedBackgroundBrush" Color="#D0D0D0" />
            
            <SolidColorBrush x:Key="TextBoxBackgroundBrush" Color="White" />
            <SolidColorBrush x:Key="TextBoxForegroundBrush" Color="Black" />
            <SolidColorBrush x:Key="TextBoxBorderBrush" Color="#CCCCCC" />
            
            <SolidColorBrush x:Key="LabelForegroundBrush" Color="Black" />
            
            <SolidColorBrush x:Key="MenuBackgroundBrush" Color="#F8F8F8" />
            <SolidColorBrush x:Key="MenuForegroundBrush" Color="Black" />
            
            <SolidColorBrush x:Key="ToolBarBackgroundBrush" Color="#F0F0F0" />
            <SolidColorBrush x:Key="ToolBarForegroundBrush" Color="Black" />
            
            <SolidColorBrush x:Key="StatusBarBackgroundBrush" Color="#F0F0F0" />
            <SolidColorBrush x:Key="StatusBarForegroundBrush" Color="Black" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
