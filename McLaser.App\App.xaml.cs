using System;
using System.Windows;
using McLaser.App.Core;

namespace McLaser.App
{
    /// <summary>
    /// McLaser示例应用程序
    /// 展示McLaser.Core框架的完整功能
    /// </summary>
    public partial class App : Application
    {
        private AppCore? _appCore;

        /// <summary>
        /// 应用程序启动事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">启动事件参数</param>
        private void Application_Startup(object sender, StartupEventArgs e)
        {
            try
            {
                // 创建应用程序核心
                _appCore = new AppCore();

                // 运行应用程序
                _appCore.Run();
            }
            catch (Exception ex)
            {
                // 显示启动错误
                MessageBox.Show(
                    $"应用程序启动失败：\n\n{ex.Message}\n\n详细信息：\n{ex}",
                    "McLaser示例应用程序 - 启动错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                // 关闭应用程序
                Shutdown(1);
            }
        }

        /// <summary>
        /// 应用程序退出事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">退出事件参数</param>
        private void Application_Exit(object sender, ExitEventArgs e)
        {
            try
            {
                // 清理应用程序核心
                _appCore?.Dispose();
            }
            catch (Exception ex)
            {
                // 记录清理错误（但不阻止应用程序退出）
                System.Diagnostics.Debug.WriteLine($"应用程序清理时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 全局异常处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">异常事件参数</param>
        private void Application_DispatcherUnhandledException(object sender, 
            System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                // 记录异常
                var message = $"未处理的异常：{e.Exception.Message}";
                System.Diagnostics.Debug.WriteLine(message);

                // 显示错误对话框
                var result = MessageBox.Show(
                    $"{message}\n\n是否继续运行应用程序？\n\n点击"是"继续，点击"否"退出应用程序。",
                    "McLaser示例应用程序 - 未处理异常",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Error);

                if (result == MessageBoxResult.Yes)
                {
                    // 标记异常已处理，继续运行
                    e.Handled = true;
                }
                else
                {
                    // 退出应用程序
                    Shutdown(1);
                }
            }
            catch
            {
                // 如果异常处理本身出错，直接退出
                Shutdown(1);
            }
        }
    }
}
