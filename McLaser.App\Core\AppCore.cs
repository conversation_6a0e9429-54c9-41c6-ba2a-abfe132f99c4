using System;
using System.Windows;
using McLaser.Core.Framework;
using McLaser.Core.Framework.Configuration;
using McLaser.Core.Framework.Container;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Framework.Services;
using McLaser.Core.Framework.UI;
using McLaser.App.ViewModels;
using McLaser.App.Views;

namespace McLaser.App.Core
{
    /// <summary>
    /// McLaser示例应用程序核心类
    /// 继承自McLaser.Core的ApplicationCoreBase，展示框架的完整使用
    /// </summary>
    public class AppCore : ApplicationCoreBase
    {
        private IThemeService? _themeService;
        private IWindowManager? _windowManager;
        private ILogger? _logger;

        /// <summary>
        /// 应用程序唯一标识符
        /// </summary>
        public override string AppId => "McLaser.App";

        /// <summary>
        /// 应用程序显示名称
        /// </summary>
        public override string AppName => "McLaser示例应用程序";

        /// <summary>
        /// 应用程序版本
        /// </summary>
        public override Version AppVersion => new Version(1, 0, 0, 0);

        /// <summary>
        /// 配置服务注册
        /// 注册应用程序特定的服务和组件
        /// </summary>
        protected override void ConfigureServices(IContainer container)
        {
            try
            {
                // 注册核心服务
                container.RegisterSingleton<IConfigurationService, DefaultConfigurationService>();
                container.RegisterSingleton<ILoggerFactory, DefaultLoggerFactory>();
                container.RegisterSingleton<ILogger>(c => c.Resolve<ILoggerFactory>().CreateLogger("McLaser.App"));
                container.RegisterSingleton<IDialogService, DefaultDialogService>();
                container.RegisterSingleton<INavigationService, DefaultNavigationService>();
                container.RegisterSingleton<IExceptionHandlingService, DefaultExceptionHandlingService>();

                // 注册UI增强服务
                container.RegisterSingleton<IThemeService, ThemeManager>();
                container.RegisterSingleton<IWindowManager, WindowManager>();

                // 注册ViewModel
                container.RegisterTransient<MainViewModel>();
                container.RegisterTransient<SettingsViewModel>();
                container.RegisterTransient<DataInputViewModel>();

                // 注册应用程序特定服务
                RegisterApplicationServices(container);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"服务注册失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建应用程序主窗口
        /// </summary>
        protected override Window CreateMainWindow()
        {
            try
            {
                // 获取服务
                var container = ContainerManager.Current;
                _themeService = container.Resolve<IThemeService>();
                _windowManager = container.Resolve<IWindowManager>();
                _logger = container.Resolve<ILogger>();

                // 初始化主题系统
                InitializeThemeSystem();

                // 初始化窗口管理
                InitializeWindowManager();

                // 加载应用程序配置
                LoadApplicationSettings();

                // 创建主窗口
                var mainViewModel = container.Resolve<MainViewModel>();
                var mainWindow = new MainWindow
                {
                    DataContext = mainViewModel
                };

                // 设置为主窗口
                _windowManager?.SetMainWindow(mainWindow);

                _logger?.LogInfo("主窗口创建完成");
                return mainWindow;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"创建主窗口失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用程序启动
        /// </summary>
        public void Run()
        {
            try
            {
                // 启动应用程序
                Start();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"应用程序启动失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 注册应用程序特定服务
        /// </summary>
        private void RegisterApplicationServices(IContainer container)
        {
            // 这里可以注册应用程序特定的服务
            // 例如：业务服务、数据服务等
        }

        /// <summary>
        /// 初始化主题系统
        /// </summary>
        private void InitializeThemeSystem()
        {
            if (_themeService == null) return;

            try
            {
                // 注册应用程序主题
                RegisterApplicationThemes();

                // 加载保存的主题设置
                _themeService.LoadSavedTheme();

                // 如果没有保存的主题，使用默认主题
                if (string.IsNullOrEmpty(_themeService.CurrentTheme) || 
                    _themeService.CurrentTheme == "Default")
                {
                    var configService = ContainerManager.Current.Resolve<IConfigurationService>();
                    var defaultTheme = configService?.GetValue<string>("DefaultTheme") ?? "Light";
                    _themeService.ApplyTheme(defaultTheme);
                }

                _logger?.LogInfo($"主题系统初始化完成，当前主题: {_themeService.CurrentTheme}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"主题系统初始化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 注册应用程序主题
        /// </summary>
        private void RegisterApplicationThemes()
        {
            if (_themeService == null) return;

            try
            {
                // 注册浅色主题
                var lightThemeUri = new Uri("pack://application:,,,/McLaser.App;component/Themes/LightTheme.xaml");
                _themeService.RegisterTheme("Light", lightThemeUri);

                // 注册深色主题
                var darkThemeUri = new Uri("pack://application:,,,/McLaser.App;component/Themes/DarkTheme.xaml");
                _themeService.RegisterTheme("Dark", darkThemeUri);

                _logger?.LogInfo("应用程序主题注册完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"主题注册失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化窗口管理器
        /// </summary>
        private void InitializeWindowManager()
        {
            if (_windowManager == null) return;

            try
            {
                // 订阅窗口事件
                _windowManager.WindowOpened += OnWindowOpened;
                _windowManager.WindowClosed += OnWindowClosed;
                _windowManager.WindowActivated += OnWindowActivated;

                _logger?.LogInfo("窗口管理器初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"窗口管理器初始化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 加载应用程序设置
        /// </summary>
        private void LoadApplicationSettings()
        {
            try
            {
                // 从配置服务加载设置
                var configService = ContainerManager.Current.Resolve<IConfigurationService>();
                var autoSave = configService?.GetValue<bool>("AutoSaveSettings") ?? true;
                var rememberWindowState = configService?.GetValue<bool>("WindowStateRemember") ?? true;
                var cacheEnabled = configService?.GetValue<bool>("CacheEnabled") ?? true;
                var performanceMonitoring = configService?.GetValue<bool>("PerformanceMonitoring") ?? true;

                _logger?.LogInfo($"应用程序设置加载完成 - 自动保存: {autoSave}, 记住窗口状态: {rememberWindowState}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"加载应用程序设置失败: {ex.Message}", ex);
            }
        }



        /// <summary>
        /// 窗口打开事件处理
        /// </summary>
        private void OnWindowOpened(object? sender, WindowEventArgs e)
        {
            _logger?.LogInfo($"窗口已打开: {e.Window.GetType().Name} (ID: {e.WindowId})");
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void OnWindowClosed(object? sender, WindowEventArgs e)
        {
            _logger?.LogInfo($"窗口已关闭: {e.Window.GetType().Name} (ID: {e.WindowId})");
        }

        /// <summary>
        /// 窗口激活事件处理
        /// </summary>
        private void OnWindowActivated(object? sender, WindowEventArgs e)
        {
            _logger?.LogInfo($"窗口已激活: {e.Window.GetType().Name} (ID: {e.WindowId})");
        }

        /// <summary>
        /// 应用程序关闭清理
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    // 保存应用程序设置
                    SaveApplicationSettings();

                    // 保存主题设置
                    _themeService?.SaveCurrentTheme();

                    // 保存窗口状态
                    _windowManager?.SaveAllWindowStates();

                    // 取消事件订阅
                    if (_windowManager != null)
                    {
                        _windowManager.WindowOpened -= OnWindowOpened;
                        _windowManager.WindowClosed -= OnWindowClosed;
                        _windowManager.WindowActivated -= OnWindowActivated;
                    }

                    _logger?.LogInfo("应用程序清理完成");
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"应用程序清理失败: {ex.Message}", ex);
                }
            }

            base.Dispose(disposing);
        }

        /// <summary>
        /// 保存应用程序设置
        /// </summary>
        private void SaveApplicationSettings()
        {
            try
            {
                // 保存当前配置到配置服务
                var configService = ContainerManager.Current.Resolve<IConfigurationService>();
                configService?.Save();
                _logger?.LogInfo("应用程序设置已保存");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"保存应用程序设置失败: {ex.Message}", ex);
            }
        }
    }
}
