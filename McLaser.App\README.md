# McLaser.App - WPF示例应用程序

## 📋 项目概述

McLaser.App是一个完整的WPF示例应用程序，展示了McLaser.Core框架的所有核心功能和最佳实践。该项目旨在为开发者提供一个实际可运行的参考实现，演示如何使用McLaser.Core框架构建企业级WPF应用程序。

## 🏗️ 项目架构

### 技术栈
- **.NET Framework 4.7.2**: 确保与McLaser.Core的兼容性
- **WPF (Windows Presentation Foundation)**: 现代化的Windows桌面应用程序框架
- **MVVM模式**: 使用McLaser.Core提供的ViewModelBase和RelayCommand
- **依赖注入**: 基于McLaser.Core的统一DI容器架构

### 项目结构
```
McLaser.App/
├── Core/                    # 应用程序核心
│   └── AppCore.cs          # 继承ApplicationCoreBase的主类
├── Views/                   # 视图层
│   ├── MainWindow.xaml     # 主窗口
│   ├── SettingsWindow.xaml # 设置窗口
│   └── DataInputWindow.xaml# 数据输入窗口
├── ViewModels/             # 视图模型层
│   ├── MainViewModel.cs    # 主窗口ViewModel
│   ├── SettingsViewModel.cs# 设置ViewModel
│   └── DataInputViewModel.cs# 数据输入ViewModel
├── Themes/                 # 主题资源
│   ├── LightTheme.xaml     # 浅色主题
│   └── DarkTheme.xaml      # 深色主题
├── App.xaml               # 应用程序定义
├── App.config             # 应用程序配置
└── McLaser.App.csproj     # 项目文件
```

## 🎯 功能特性

### 1. 框架集成演示
- **ApplicationCoreBase继承**: 展示如何正确继承和扩展框架核心类
- **统一DI容器**: 演示服务注册、依赖注入和生命周期管理
- **MVVM模式**: 完整的数据绑定、命令模式和属性通知机制

### 2. UI功能演示
- **主题管理系统**: Light/Dark主题动态切换
- **窗口管理器**: 多窗口管理、状态保存和恢复
- **响应式布局**: 自适应窗口大小和分辨率

### 3. 数据验证框架
- **实时验证**: 基于DataAnnotations的属性验证
- **错误显示**: 清晰的验证错误提示和状态指示
- **验证规则**: 支持Required、StringLength、Range、Email等验证

### 4. 服务功能演示
- **配置服务**: 设置的保存、读取和管理
- **对话框服务**: 信息、警告、确认和错误对话框
- **日志服务**: 完整的日志记录和管理
- **导航服务**: 窗口间导航和状态管理

## 🚀 快速开始

### 环境要求
- Visual Studio 2019或更高版本
- .NET Framework 4.7.2或更高版本
- Windows 10或更高版本

### 编译和运行
1. **克隆项目**
   ```bash
   git clone [repository-url]
   cd McLaser_V1
   ```

2. **还原依赖**
   ```bash
   dotnet restore McLaser_V1.sln
   ```

3. **编译项目**
   ```bash
   dotnet build McLaser_V1.sln
   ```

4. **运行应用程序**
   ```bash
   dotnet run --project McLaser.App
   ```

### 使用Visual Studio
1. 打开`McLaser_V1.sln`解决方案文件
2. 设置`McLaser.App`为启动项目
3. 按F5运行或点击"开始调试"

## 📖 功能演示

### 主窗口功能
- **菜单栏**: 文件、编辑、视图、工具、帮助菜单
- **工具栏**: 常用功能的快速访问按钮
- **状态栏**: 实时状态信息和主题切换
- **功能面板**: 框架特性介绍和操作按钮

### 主题管理
- **动态切换**: 运行时无需重启即可切换主题
- **主题保存**: 自动保存用户的主题偏好
- **资源管理**: 完整的主题资源定义和管理

### 数据验证
- **表单验证**: 完整的用户信息输入和验证
- **实时反馈**: 输入时即时显示验证结果
- **错误管理**: 统一的验证错误收集和显示

### 设置管理
- **配置界面**: 用户友好的设置界面
- **实时预览**: 设置变更的即时预览
- **持久化**: 设置的自动保存和恢复

## 🔧 开发指南

### 扩展应用程序
1. **添加新窗口**: 在Views文件夹中创建新的XAML文件
2. **创建ViewModel**: 继承ViewModelBase创建对应的ViewModel
3. **注册服务**: 在AppCore.cs中注册新的服务和ViewModel
4. **配置导航**: 使用WindowManager管理窗口显示

### 自定义主题
1. **创建主题文件**: 在Themes文件夹中创建新的ResourceDictionary
2. **定义颜色**: 按照现有主题的结构定义颜色资源
3. **注册主题**: 在ThemeManager中注册新主题
4. **测试主题**: 确保所有控件在新主题下正常显示

### 添加验证规则
1. **自定义特性**: 继承ValidationAttribute创建自定义验证
2. **注册规则**: 在ValidationEngine中注册自定义规则
3. **应用验证**: 在ViewModel属性上应用验证特性
4. **错误处理**: 确保错误信息正确显示

## 📚 相关文档

- [使用指南.md](使用指南.md) - 详细的使用说明
- [功能演示指南.md](功能演示指南.md) - 功能演示步骤
- [McLaser.Core框架文档](../McLaser.Core/README.md) - 核心框架文档

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个示例应用程序。请确保：
1. 遵循现有的代码风格和架构模式
2. 添加适当的中文注释和XML文档
3. 确保所有功能都有相应的演示
4. 更新相关文档

## 📄 许可证

本项目采用MIT许可证，详情请参阅LICENSE文件。

## 🙏 致谢

感谢McLaser.Core框架团队提供的优秀基础架构，使得构建这样一个功能完整的示例应用程序成为可能。
