using System;
using System.Collections.Generic;
using System.Configuration;
using McLaser.Core.Framework.Configuration;

namespace McLaser.App.Services
{
    /// <summary>
    /// 默认配置服务实现
    /// </summary>
    public class DefaultConfigurationService : IConfigurationService
    {
        private readonly Dictionary<string, object> _settings = new Dictionary<string, object>();

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public T? GetValue<T>(string key, T? defaultValue = default)
        {
            if (string.IsNullOrEmpty(key))
                return defaultValue;

            try
            {
                // 首先尝试从内存缓存获取
                if (_settings.TryGetValue(key, out var cachedValue))
                {
                    if (cachedValue is T typedValue)
                        return typedValue;
                }

                // 然后尝试从App.config获取
                var configValue = ConfigurationManager.AppSettings[key];
                if (!string.IsNullOrEmpty(configValue))
                {
                    var convertedValue = Convert.ChangeType(configValue, typeof(T));
                    if (convertedValue is T result)
                    {
                        _settings[key] = result;
                        return result;
                    }
                }

                return defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public void SetValue<T>(string key, T value)
        {
            if (string.IsNullOrEmpty(key))
                return;

            _settings[key] = value ?? throw new ArgumentNullException(nameof(value));
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void Save()
        {
            // 在实际应用中，这里应该将配置保存到持久化存储
            // 这里只是一个简单的实现
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void Reload()
        {
            _settings.Clear();
            ConfigurationManager.RefreshSection("appSettings");
        }
    }
}
