using System;
using System.Windows;
using McLaser.Core.Framework.Services;

namespace McLaser.App.Services
{
    /// <summary>
    /// 默认对话框服务实现
    /// </summary>
    public class DefaultDialogService : IDialogService
    {
        /// <summary>
        /// 显示消息对话框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">对话框标题</param>
        /// <param name="messageType">消息类型</param>
        /// <returns>用户选择结果</returns>
        public bool? ShowMessage(string message, string title = "消息", MessageType messageType = MessageType.Information)
        {
            MessageBoxImage icon;
            MessageBoxButton button;

            switch (messageType)
            {
                case MessageType.Information:
                    icon = MessageBoxImage.Information;
                    button = MessageBoxButton.OK;
                    break;
                case MessageType.Warning:
                    icon = MessageBoxImage.Warning;
                    button = MessageBoxButton.OK;
                    break;
                case MessageType.Error:
                    icon = MessageBoxImage.Error;
                    button = MessageBoxButton.OK;
                    break;
                case MessageType.Question:
                    icon = MessageBoxImage.Question;
                    button = MessageBoxButton.YesNo;
                    break;
                default:
                    icon = MessageBoxImage.Information;
                    button = MessageBoxButton.OK;
                    break;
            }

            var result = MessageBox.Show(message, title, button, icon);

            switch (result)
            {
                case MessageBoxResult.Yes:
                case MessageBoxResult.OK:
                    return true;
                case MessageBoxResult.No:
                    return false;
                default:
                    return null;
            }
        }
    }

    /// <summary>
    /// 消息类型枚举
    /// </summary>
    public enum MessageType
    {
        Information,
        Warning,
        Error,
        Question
    }
}
