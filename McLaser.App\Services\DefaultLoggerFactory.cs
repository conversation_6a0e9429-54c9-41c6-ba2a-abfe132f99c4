using System;
using System.Diagnostics;
using McLaser.Core.Framework.Logging;

namespace McLaser.App.Services
{
    /// <summary>
    /// 默认日志工厂实现
    /// </summary>
    public class DefaultLoggerFactory : ILoggerFactory
    {
        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <param name="categoryName">类别名称</param>
        /// <returns>日志记录器</returns>
        public ILogger CreateLogger(string categoryName)
        {
            return new DefaultLogger(categoryName);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 清理资源
        }
    }

    /// <summary>
    /// 默认日志记录器实现
    /// </summary>
    public class DefaultLogger : ILogger
    {
        private readonly string _categoryName;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="categoryName">类别名称</param>
        public DefaultLogger(string categoryName)
        {
            _categoryName = categoryName ?? throw new ArgumentNullException(nameof(categoryName));
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogInfo(string message)
        {
            WriteLog("INFO", message);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogWarning(string message)
        {
            WriteLog("WARN", message);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogError(string message)
        {
            WriteLog("ERROR", message);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogDebug(string message)
        {
            WriteLog("DEBUG", message);
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        private void WriteLog(string level, string message)
        {
            var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{level}] [{_categoryName}] {message}";
            Debug.WriteLine(logMessage);
            Console.WriteLine(logMessage);
        }
    }
}
