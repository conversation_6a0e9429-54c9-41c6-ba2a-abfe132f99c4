using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Core.Framework.Configuration;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Framework.Services;
using McLaser.Core.Framework.UI;

namespace McLaser.App.ViewModels
{
    /// <summary>
    /// 设置窗口ViewModel
    /// 展示配置管理和主题切换功能
    /// </summary>
    public class SettingsViewModel : ViewModelBase
    {
        private readonly IThemeService _themeService;
        private readonly IConfigurationService _configurationService;
        private readonly IDialogService _dialogService;
        private readonly ILogger _logger;

        private string _selectedTheme = "Light";
        private bool _autoSaveSettings = true;
        private bool _rememberWindowState = true;
        private bool _enableLogging = true;
        private bool _enableCaching = true;
        private bool _enablePerformanceMonitoring = true;
        private string _logLevel = "Info";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="themeService">主题服务</param>
        /// <param name="configurationService">配置服务</param>
        /// <param name="dialogService">对话框服务</param>
        /// <param name="logger">日志服务</param>
        public SettingsViewModel(
            IThemeService themeService,
            IConfigurationService configurationService,
            IDialogService dialogService,
            ILogger logger)
        {
            _themeService = themeService ?? throw new ArgumentNullException(nameof(themeService));
            _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
            _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeCommands();
            LoadSettings();
        }

        #region 属性

        /// <summary>
        /// 选中的主题
        /// </summary>
        public string SelectedTheme
        {
            get => _selectedTheme;
            set => SetProperty(ref _selectedTheme, value);
        }

        /// <summary>
        /// 自动保存设置
        /// </summary>
        public bool AutoSaveSettings
        {
            get => _autoSaveSettings;
            set => SetProperty(ref _autoSaveSettings, value);
        }

        /// <summary>
        /// 记住窗口状态
        /// </summary>
        public bool RememberWindowState
        {
            get => _rememberWindowState;
            set => SetProperty(ref _rememberWindowState, value);
        }

        /// <summary>
        /// 启用日志记录
        /// </summary>
        public bool EnableLogging
        {
            get => _enableLogging;
            set => SetProperty(ref _enableLogging, value);
        }

        /// <summary>
        /// 启用缓存
        /// </summary>
        public bool EnableCaching
        {
            get => _enableCaching;
            set => SetProperty(ref _enableCaching, value);
        }

        /// <summary>
        /// 启用性能监控
        /// </summary>
        public bool EnablePerformanceMonitoring
        {
            get => _enablePerformanceMonitoring;
            set => SetProperty(ref _enablePerformanceMonitoring, value);
        }

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel
        {
            get => _logLevel;
            set => SetProperty(ref _logLevel, value);
        }

        /// <summary>
        /// 可用主题列表
        /// </summary>
        public ObservableCollection<string> AvailableThemes { get; } = new ObservableCollection<string>();

        /// <summary>
        /// 可用日志级别列表
        /// </summary>
        public ObservableCollection<string> AvailableLogLevels { get; } = new ObservableCollection<string>
        {
            "Debug", "Info", "Warning", "Error", "Fatal"
        };

        #endregion

        #region 命令

        /// <summary>
        /// 应用设置命令
        /// </summary>
        public ICommand ApplySettingsCommand { get; private set; } = null!;

        /// <summary>
        /// 保存设置命令
        /// </summary>
        public ICommand SaveSettingsCommand { get; private set; } = null!;

        /// <summary>
        /// 重置设置命令
        /// </summary>
        public ICommand ResetSettingsCommand { get; private set; } = null!;

        /// <summary>
        /// 关闭窗口命令
        /// </summary>
        public ICommand CloseCommand { get; private set; } = null!;

        /// <summary>
        /// 预览主题命令
        /// </summary>
        public ICommand PreviewThemeCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            ApplySettingsCommand = new RelayCommand(ExecuteApplySettings, CanExecuteApplySettings);
            SaveSettingsCommand = new RelayCommand(ExecuteSaveSettings);
            ResetSettingsCommand = new RelayCommand(ExecuteResetSettings);
            CloseCommand = new RelayCommand<System.Windows.Window>(ExecuteClose);
            PreviewThemeCommand = new RelayCommand<string>(ExecutePreviewTheme);
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                // 加载可用主题
                LoadAvailableThemes();

                // 加载当前设置
                SelectedTheme = _themeService.CurrentTheme;
                AutoSaveSettings = _configurationService.GetValue<bool>("AutoSaveSettings", true);
                RememberWindowState = _configurationService.GetValue<bool>("WindowStateRemember", true);
                EnableLogging = _configurationService.GetValue<bool>("EnableLogging", true);
                EnableCaching = _configurationService.GetValue<bool>("CacheEnabled", true);
                EnablePerformanceMonitoring = _configurationService.GetValue<bool>("PerformanceMonitoring", true);
                LogLevel = _configurationService.GetValue<string>("LogLevel", "Info");

                _logger.LogInfo("设置已加载");
            }
            catch (Exception ex)
            {
                _logger.LogError($"加载设置失败: {ex.Message}", ex);
                _dialogService.ShowError($"加载设置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 加载可用主题
        /// </summary>
        private void LoadAvailableThemes()
        {
            AvailableThemes.Clear();
            foreach (var theme in _themeService.AvailableThemes)
            {
                AvailableThemes.Add(theme);
            }
        }

        /// <summary>
        /// 验证设置
        /// </summary>
        /// <returns>验证是否通过</returns>
        private bool ValidateSettings()
        {
            if (string.IsNullOrEmpty(SelectedTheme))
            {
                _dialogService.ShowWarning("请选择一个主题。", "验证失败");
                return false;
            }

            if (string.IsNullOrEmpty(LogLevel))
            {
                _dialogService.ShowWarning("请选择日志级别。", "验证失败");
                return false;
            }

            return true;
        }

        #endregion

        #region 命令执行方法

        /// <summary>
        /// 执行应用设置命令
        /// </summary>
        private void ExecuteApplySettings()
        {
            try
            {
                if (!ValidateSettings()) return;

                // 应用主题
                if (_themeService.CurrentTheme != SelectedTheme)
                {
                    if (_themeService.ApplyTheme(SelectedTheme))
                    {
                        _logger.LogInfo($"主题已应用: {SelectedTheme}");
                    }
                    else
                    {
                        _dialogService.ShowWarning($"应用主题失败: {SelectedTheme}", "警告");
                        return;
                    }
                }

                // 应用其他设置
                _configurationService.SetValue("AutoSaveSettings", AutoSaveSettings);
                _configurationService.SetValue("WindowStateRemember", RememberWindowState);
                _configurationService.SetValue("EnableLogging", EnableLogging);
                _configurationService.SetValue("CacheEnabled", EnableCaching);
                _configurationService.SetValue("PerformanceMonitoring", EnablePerformanceMonitoring);
                _configurationService.SetValue("LogLevel", LogLevel);

                _dialogService.ShowInformation("设置已应用。", "成功");
                _logger.LogInfo("设置已应用");
            }
            catch (Exception ex)
            {
                _logger.LogError($"应用设置失败: {ex.Message}", ex);
                _dialogService.ShowError($"应用设置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否可以执行应用设置命令
        /// </summary>
        /// <returns>是否可以执行</returns>
        private bool CanExecuteApplySettings()
        {
            return !string.IsNullOrEmpty(SelectedTheme) && !string.IsNullOrEmpty(LogLevel);
        }

        /// <summary>
        /// 执行保存设置命令
        /// </summary>
        private void ExecuteSaveSettings()
        {
            try
            {
                // 先应用设置
                ExecuteApplySettings();

                // 保存配置
                _configurationService.Save();

                // 保存主题设置
                _themeService.SaveCurrentTheme();

                _dialogService.ShowInformation("设置已保存。", "成功");
                _logger.LogInfo("设置已保存");
            }
            catch (Exception ex)
            {
                _logger.LogError($"保存设置失败: {ex.Message}", ex);
                _dialogService.ShowError($"保存设置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 执行重置设置命令
        /// </summary>
        private void ExecuteResetSettings()
        {
            try
            {
                var result = _dialogService.ShowConfirmation("确定要重置所有设置为默认值吗？", "确认重置");
                if (!result) return;

                // 重置为默认值
                SelectedTheme = "Light";
                AutoSaveSettings = true;
                RememberWindowState = true;
                EnableLogging = true;
                EnableCaching = true;
                EnablePerformanceMonitoring = true;
                LogLevel = "Info";

                _dialogService.ShowInformation("设置已重置为默认值。", "重置完成");
                _logger.LogInfo("设置已重置");
            }
            catch (Exception ex)
            {
                _logger.LogError($"重置设置失败: {ex.Message}", ex);
                _dialogService.ShowError($"重置设置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 执行关闭窗口命令
        /// </summary>
        /// <param name="window">窗口实例</param>
        private void ExecuteClose(System.Windows.Window? window)
        {
            window?.Close();
        }

        /// <summary>
        /// 执行预览主题命令
        /// </summary>
        /// <param name="themeName">主题名称</param>
        private void ExecutePreviewTheme(string? themeName)
        {
            if (string.IsNullOrEmpty(themeName)) return;

            try
            {
                // 获取主题预览信息
                var preview = _themeService.GetThemePreview(themeName);
                if (preview != null)
                {
                    var message = $"主题预览：{preview.DisplayName}\n\n" +
                                 $"描述：{preview.Description}\n" +
                                 $"作者：{preview.Author}\n" +
                                 $"版本：{preview.Version}\n" +
                                 $"类型：{(preview.IsDarkTheme ? "深色主题" : "浅色主题")}";

                    _dialogService.ShowInformation(message, "主题预览");
                }
                else
                {
                    _dialogService.ShowWarning($"无法获取主题预览信息: {themeName}", "预览失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"预览主题失败: {ex.Message}", ex);
                _dialogService.ShowError($"预览主题失败：{ex.Message}");
            }
        }

        #endregion
    }
}
