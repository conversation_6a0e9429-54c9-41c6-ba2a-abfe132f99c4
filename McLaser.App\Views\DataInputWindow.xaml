<Window x:Class="McLaser.App.Views.DataInputWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="数据输入 - 验证框架演示" Height="600" Width="700"
        MinHeight="500" MinWidth="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,15,0,10" />
            <Setter Property="Foreground" Value="{DynamicResource AccentBrush}" />
        </Style>

        <Style x:Key="FieldLabelStyle" TargetType="Label">
            <Setter Property="Width" Value="100" />
            <Setter Property="VerticalAlignment" Value="Top" />
            <Setter Property="Margin" Value="0,5" />
            <Setter Property="HorizontalAlignment" Value="Left" />
        </Style>

        <Style x:Key="FieldTextBoxStyle" TargetType="TextBox">
            <Setter Property="Margin" Value="110,5,0,5" />
            <Setter Property="Padding" Value="5" />
            <Setter Property="Height" Value="25" />
            <Style.Triggers>
                <Trigger Property="Validation.HasError" Value="True">
                    <Setter Property="BorderBrush" Value="Red" />
                    <Setter Property="BorderThickness" Value="2" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="FieldTextBoxMultilineStyle" TargetType="TextBox" BasedOn="{StaticResource FieldTextBoxStyle}">
            <Setter Property="Height" Value="80" />
            <Setter Property="TextWrapping" Value="Wrap" />
            <Setter Property="AcceptsReturn" Value="True" />
            <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        </Style>

        <Style x:Key="ValidationErrorStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="Red" />
            <Setter Property="FontSize" Value="11" />
            <Setter Property="Margin" Value="110,0,0,5" />
            <Setter Property="TextWrapping" Value="Wrap" />
        </Style>

        <Style x:Key="SuccessTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="Green" />
            <Setter Property="FontWeight" Value="Bold" />
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*" />
            <ColumnDefinition Width="5" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <!-- 左侧：数据输入表单 -->
        <ScrollViewer Grid.Column="0" Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <TextBlock Text="个人信息输入" Style="{StaticResource SectionHeaderStyle}" />
                <TextBlock Text="请填写以下信息，系统将自动进行数据验证。" 
                           Margin="0,0,0,15" Foreground="{DynamicResource DisabledForegroundBrush}" />
                
                <!-- 姓名 -->
                <Grid Margin="0,0,0,10">
                    <Label Content="姓名 *：" Style="{StaticResource FieldLabelStyle}" />
                    <TextBox Text="{Binding Name, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}" 
                             Style="{StaticResource FieldTextBoxStyle}" />
                </Grid>
                
                <!-- 电子邮箱 -->
                <Grid Margin="0,0,0,10">
                    <Label Content="邮箱 *：" Style="{StaticResource FieldLabelStyle}" />
                    <TextBox Text="{Binding Email, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}" 
                             Style="{StaticResource FieldTextBoxStyle}" />
                </Grid>
                
                <!-- 年龄 -->
                <Grid Margin="0,0,0,10">
                    <Label Content="年龄 *：" Style="{StaticResource FieldLabelStyle}" />
                    <TextBox Text="{Binding Age, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}" 
                             Style="{StaticResource FieldTextBoxStyle}" />
                </Grid>
                
                <!-- 电话号码 -->
                <Grid Margin="0,0,0,10">
                    <Label Content="电话：" Style="{StaticResource FieldLabelStyle}" />
                    <TextBox Text="{Binding Phone, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}" 
                             Style="{StaticResource FieldTextBoxStyle}" />
                </Grid>
                
                <!-- 网站地址 -->
                <Grid Margin="0,0,0,10">
                    <Label Content="网站：" Style="{StaticResource FieldLabelStyle}" />
                    <TextBox Text="{Binding Website, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}" 
                             Style="{StaticResource FieldTextBoxStyle}" />
                </Grid>
                
                <!-- 描述 -->
                <Grid Margin="0,0,0,15">
                    <Label Content="描述：" Style="{StaticResource FieldLabelStyle}" />
                    <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}" 
                             Style="{StaticResource FieldTextBoxMultilineStyle}" />
                </Grid>
                
                <!-- 验证状态 -->
                <TextBlock Text="验证状态" Style="{StaticResource SectionHeaderStyle}" />
                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="数据验证：" VerticalAlignment="Center" />
                    <TextBlock VerticalAlignment="Center" Margin="10,0,0,0">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Setter Property="Text" Value="通过" />
                                <Setter Property="Foreground" Value="Green" />
                                <Setter Property="FontWeight" Value="Bold" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsValid}" Value="False">
                                        <Setter Property="Text" Value="失败" />
                                        <Setter Property="Foreground" Value="Red" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                </StackPanel>
                
                <!-- 操作按钮 -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                    <Button Content="验证数据" Command="{Binding ValidateCommand}" 
                            Margin="0,0,10,0" Padding="15,8" />
                    <Button Content="保存数据" Command="{Binding SaveCommand}" 
                            Margin="0,0,10,0" Padding="15,8" />
                    <Button Content="清空数据" Command="{Binding ClearCommand}" 
                            Margin="0,0,10,0" Padding="15,8" />
                    <Button Content="生成测试数据" Command="{Binding GenerateTestDataCommand}" 
                            Padding="15,8" />
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 分隔符 -->
        <GridSplitter Grid.Column="1" Grid.Row="0" HorizontalAlignment="Stretch" 
                      Background="{DynamicResource BorderBrush}" />
        
        <!-- 右侧：验证信息 -->
        <GroupBox Grid.Column="2" Grid.Row="0" Header="验证信息" Padding="10">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- 验证结果 -->
                    <TextBlock Text="验证结果：" FontWeight="Bold" Margin="0,0,0,10" />
                    <TextBlock Margin="0,0,0,15">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Setter Property="Text" Value="数据验证通过" />
                                <Setter Property="Foreground" Value="Green" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsValid}" Value="False">
                                        <Setter Property="Text" Value="数据验证失败" />
                                        <Setter Property="Foreground" Value="Red" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    
                    <!-- 验证错误列表 -->
                    <TextBlock Text="验证错误：" FontWeight="Bold" Margin="0,0,0,10" />
                    <ListBox ItemsSource="{Binding ValidationErrors}" 
                             Height="200" 
                             Margin="0,0,0,15">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" TextWrapping="Wrap" 
                                           Foreground="Red" FontSize="11" />
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                    
                    <!-- 验证说明 -->
                    <TextBlock Text="验证规则：" FontWeight="Bold" Margin="0,0,0,10" />
                    <TextBlock TextWrapping="Wrap" FontSize="11" 
                               Foreground="{DynamicResource DisabledForegroundBrush}">
                        <Run Text="• 姓名：必填，2-50个字符" /><LineBreak />
                        <Run Text="• 邮箱：必填，有效邮箱格式" /><LineBreak />
                        <Run Text="• 年龄：必填，1-120之间" /><LineBreak />
                        <Run Text="• 电话：可选，有效电话格式" /><LineBreak />
                        <Run Text="• 网站：可选，有效URL格式" /><LineBreak />
                        <Run Text="• 描述：可选，最多500字符" />
                    </TextBlock>
                </StackPanel>
            </ScrollViewer>
        </GroupBox>
        
        <!-- 底部按钮 -->
        <Border Grid.Column="0" Grid.ColumnSpan="3" Grid.Row="1" 
                BorderThickness="0,1,0,0" BorderBrush="{DynamicResource BorderBrush}" 
                Padding="0,15,0,0" Margin="0,15,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="关闭" Command="{Binding CloseCommand}" 
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Padding="15,8" MinWidth="80" IsCancel="True" />
            </StackPanel>
        </Border>
    </Grid>
</Window>
