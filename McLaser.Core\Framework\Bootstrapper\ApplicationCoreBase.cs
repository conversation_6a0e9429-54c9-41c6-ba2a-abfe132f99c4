﻿#nullable enable
using System;
using System.Windows;

namespace McLaser.Core.Framework
{
    /// <summary>
    /// 应用程序核心基类，实现了IApplicationCore接口
    /// </summary>
    public abstract class ApplicationCoreBase : IApplicationCore, IDisposable
    {
        protected IServiceProvider? ServiceProvider { get; private set; }

        // 抽象属性 - 必须由子类实现
        public abstract string AppId { get; }
        public abstract string AppName { get; }
        public abstract Version AppVersion { get; }

        /// <summary>
        /// 配置服务注册 - 必须由子类实现
        /// </summary>
        protected abstract void ConfigureServices(Container.IContainer container);

        /// <summary>
        /// 配置应用程序设置 - 可选覆盖
        /// </summary>
        protected virtual void ConfigureSettings()
        {
            // 默认配置实现
        }

        public virtual void Start()
        {
            // 1. 初始化容器
            InitializeContainer();

            // 2. 配置设置
            ConfigureSettings();

            // 3. 注册应用程序核心服务
            RegisterApplicationServices();

            // 4. 子类配置服务
            ConfigureServices(Container.ContainerManager.Current);

            // 5. 验证容器配置
            ValidateContainer();

            // 6. 设置服务提供者
            ServiceProvider = new ContainerServiceProviderAdapter(Container.ContainerManager.Current);

            // 7. 创建主窗口
            var mainWindow = CreateMainWindow();
            mainWindow?.Show();
        }

        public virtual void Shutdown()
        {
            // 清理资源
            if (ServiceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }

            ServiceProvider = null;
            Container.ContainerManager.Reset();
        }

        /// <summary>
        /// 初始化容器 - 可由子类重写以选择不同的容器类型
        /// </summary>
        protected virtual void InitializeContainer()
        {
            // 默认使用默认容器，子类可以重写以使用MEF或其他容器
            Container.ContainerManager.UseDefaultContainer();
        }

        /// <summary>
        /// 注册应用程序服务
        /// </summary>
        protected virtual void RegisterApplicationServices()
        {
            var container = Container.ContainerManager.Current;

            // 注册应用程序核心
            container.RegisterSingleton<IApplicationCore>(this);

            // 注册应用程序信息
            container.RegisterFactory<ApplicationInfo>(c => new ApplicationInfo
            {
                Id = AppId,
                Name = AppName,
                Version = AppVersion
            });
        }

        /// <summary>
        /// 验证容器配置
        /// </summary>
        protected virtual void ValidateContainer()
        {
            var validationResult = Container.ContainerManager.ValidateConfiguration();
            if (!validationResult.IsValid)
            {
                var errors = string.Join(Environment.NewLine, validationResult.Errors);
                throw new InvalidOperationException($"容器配置验证失败:{Environment.NewLine}{errors}");
            }
        }

        /// <summary>
        /// 创建应用程序主窗口 - 必须由子类实现
        /// </summary>
        protected abstract Window CreateMainWindow();

        /// <summary>
        /// 获取服务
        /// </summary>
        protected T GetService<T>() where T : class
        {
            return ServiceProvider?.GetService<T>() ??
                throw new InvalidOperationException("服务提供者未初始化");
        }

        public void Dispose()
        {
            Shutdown();
            GC.SuppressFinalize(this);
        }
    }
}
