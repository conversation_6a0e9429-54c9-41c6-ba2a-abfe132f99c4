using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;

namespace McLaser.Core.Framework.Configuration
{
    /// <summary>
    /// 默认配置服务实现
    /// 基于内存存储和应用程序配置
    /// </summary>
    public class DefaultConfigurationService : IConfigurationService
    {
        private readonly Dictionary<string, object> _configValues = new();
        private readonly object _lockObject = new object();

        /// <summary>
        /// 配置变更事件
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

        /// <summary>
        /// 初始化配置服务
        /// </summary>
        public DefaultConfigurationService()
        {
            LoadConfiguration();
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public T GetValue<T>(string key, T defaultValue = default(T)!)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("配置键不能为空", nameof(key));

            lock (_lockObject)
            {
                if (_configValues.TryGetValue(key, out var value))
                {
                    try
                    {
                        return ConvertValue<T>(value);
                    }
                    catch
                    {
                        return defaultValue;
                    }
                }

                // 尝试从应用程序配置中获取
                try
                {
                    var appSetting = ConfigurationManager.AppSettings[key];
                    if (!string.IsNullOrEmpty(appSetting))
                    {
                        var convertedValue = ConvertValue<T>(appSetting);
                        if (convertedValue != null)
                        {
                            _configValues[key] = convertedValue;
                            return convertedValue;
                        }
                        return convertedValue;
                    }
                }
                catch
                {
                    // 忽略转换错误
                }

                return defaultValue;
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public void SetValue<T>(string key, T value)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("配置键不能为空", nameof(key));

            lock (_lockObject)
            {
                var oldValue = _configValues.TryGetValue(key, out var existing) ? existing : null;
                var changeType = oldValue == null ? ConfigurationChangeType.Added : ConfigurationChangeType.Modified;

                _configValues[key] = value ?? throw new ArgumentNullException(nameof(value));

                // 触发变更事件
                OnConfigurationChanged(new ConfigurationChangedEventArgs(key, oldValue, value, changeType));
            }
        }

        /// <summary>
        /// 获取配置节
        /// </summary>
        /// <typeparam name="T">配置节类型</typeparam>
        /// <param name="sectionName">节名称</param>
        /// <returns>配置节对象</returns>
        public T GetSection<T>(string sectionName) where T : class, new()
        {
            if (string.IsNullOrEmpty(sectionName))
                throw new ArgumentException("节名称不能为空", nameof(sectionName));

            lock (_lockObject)
            {
                if (_configValues.TryGetValue(sectionName, out var value) && value is T section)
                {
                    return section;
                }

                return new T();
            }
        }

        /// <summary>
        /// 设置配置节
        /// </summary>
        /// <typeparam name="T">配置节类型</typeparam>
        /// <param name="sectionName">节名称</param>
        /// <param name="section">配置节对象</param>
        public void SetSection<T>(string sectionName, T section) where T : class
        {
            if (string.IsNullOrEmpty(sectionName))
                throw new ArgumentException("节名称不能为空", nameof(sectionName));

            if (section == null)
                throw new ArgumentNullException(nameof(section));

            lock (_lockObject)
            {
                var oldValue = _configValues.TryGetValue(sectionName, out var existing) ? existing : null;
                var changeType = oldValue == null ? ConfigurationChangeType.Added : ConfigurationChangeType.Modified;

                _configValues[sectionName] = section;

                // 触发变更事件
                OnConfigurationChanged(new ConfigurationChangedEventArgs(sectionName, oldValue, section, changeType));
            }
        }

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        public bool ContainsKey(string key)
        {
            if (string.IsNullOrEmpty(key))
                return false;

            lock (_lockObject)
            {
                return _configValues.ContainsKey(key) || !string.IsNullOrEmpty(ConfigurationManager.AppSettings[key]);
            }
        }

        /// <summary>
        /// 删除配置键
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否删除成功</returns>
        public bool RemoveKey(string key)
        {
            if (string.IsNullOrEmpty(key))
                return false;

            lock (_lockObject)
            {
                if (_configValues.TryGetValue(key, out var oldValue))
                {
                    _configValues.Remove(key);
                    OnConfigurationChanged(new ConfigurationChangedEventArgs(key, oldValue, null, ConfigurationChangeType.Removed));
                    return true;
                }
                return false;
            }
        }

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键集合</returns>
        public IEnumerable<string> GetAllKeys()
        {
            lock (_lockObject)
            {
                var keys = new HashSet<string>(_configValues.Keys);
                
                // 添加应用程序配置中的键
                foreach (string key in ConfigurationManager.AppSettings.AllKeys)
                {
                    keys.Add(key);
                }

                return keys.ToList();
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void Save()
        {
            // 简化实现：配置保存在内存中，应用程序重启后丢失
            // 在实际项目中可以实现到文件或注册表的持久化
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void Reload()
        {
            lock (_lockObject)
            {
                _configValues.Clear();
                LoadConfiguration();
            }
        }

        /// <summary>
        /// 清除所有配置
        /// </summary>
        public void Clear()
        {
            lock (_lockObject)
            {
                _configValues.Clear();
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            // 从应用程序配置中加载设置
            try
            {
                foreach (string key in ConfigurationManager.AppSettings.AllKeys)
                {
                    var value = ConfigurationManager.AppSettings[key];
                    if (!string.IsNullOrEmpty(value))
                    {
                        _configValues[key] = value;
                    }
                }
            }
            catch
            {
                // 忽略加载错误，使用默认配置
            }
        }

        /// <summary>
        /// 转换值类型
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="value">原始值</param>
        /// <returns>转换后的值</returns>
        private static T ConvertValue<T>(object value)
        {
            if (value is T directValue)
                return directValue;

            return (T)Convert.ChangeType(value, typeof(T));
        }

        /// <summary>
        /// 触发配置变更事件
        /// </summary>
        /// <param name="args">事件参数</param>
        protected virtual void OnConfigurationChanged(ConfigurationChangedEventArgs args)
        {
            ConfigurationChanged?.Invoke(this, args);
        }
    }
}
