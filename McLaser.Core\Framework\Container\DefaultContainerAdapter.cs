using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using McLaser.Core.Framework;

namespace McLaser.Core.Framework.Container
{
    /// <summary>
    /// 默认容器适配器
    /// 基于DefaultServiceRegistry实现的容器适配器
    /// </summary>
    public class DefaultContainerAdapter : IContainer
    {
        private readonly DefaultServiceRegistry _serviceRegistry;
        private readonly ConcurrentDictionary<string, object> _keyedServices = new();
        private readonly ConcurrentDictionary<Type, Func<IContainer, object>> _factories = new();
        private readonly ContainerStatistics _statistics = new();
        private readonly ConcurrentDictionary<Type, object> _singletonCache = new();
        private readonly object _lockObject = new object();
        private bool _disposed;

        /// <summary>
        /// 容器统计信息
        /// </summary>
        public IContainerStatistics Statistics => _statistics;

        /// <summary>
        /// 服务解析事件
        /// </summary>
        public event EventHandler<ServiceResolvedEventArgs>? ServiceResolved;

        /// <summary>
        /// 服务注册事件
        /// </summary>
        public event EventHandler<ServiceRegisteredEventArgs>? ServiceRegistered;

        /// <summary>
        /// 初始化默认容器适配器
        /// </summary>
        /// <param name="serviceRegistry">服务注册器</param>
        public DefaultContainerAdapter(DefaultServiceRegistry? serviceRegistry = null)
        {
            _serviceRegistry = serviceRegistry ?? new DefaultServiceRegistry();
        }

        /// <summary>
        /// 注册单例服务
        /// </summary>
        /// <typeparam name="TService">服务接口类型</typeparam>
        /// <typeparam name="TImplementation">服务实现类型</typeparam>
        public void RegisterSingleton<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService
        {
            _serviceRegistry.RegisterSingleton<TService, TImplementation>();
            OnServiceRegistered(typeof(TService), typeof(TImplementation), ServiceLifetime.Singleton);
        }

        /// <summary>
        /// 注册单例服务实例
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="instance">服务实例</param>
        public void RegisterSingleton<TService>(TService instance) where TService : class
        {
            if (instance == null)
                throw new ArgumentNullException(nameof(instance));

            _serviceRegistry.RegisterSingleton(instance);
            OnServiceRegistered(typeof(TService), instance.GetType(), ServiceLifetime.Singleton);
        }

        /// <summary>
        /// 注册瞬态服务
        /// </summary>
        /// <typeparam name="TService">服务接口类型</typeparam>
        /// <typeparam name="TImplementation">服务实现类型</typeparam>
        public void RegisterTransient<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService
        {
            _serviceRegistry.RegisterTransient<TService, TImplementation>();
            OnServiceRegistered(typeof(TService), typeof(TImplementation), ServiceLifetime.Transient);
        }

        /// <summary>
        /// 注册作用域服务
        /// </summary>
        /// <typeparam name="TService">服务接口类型</typeparam>
        /// <typeparam name="TImplementation">服务实现类型</typeparam>
        public void RegisterScoped<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService
        {
            _serviceRegistry.RegisterScoped<TService, TImplementation>();
            OnServiceRegistered(typeof(TService), typeof(TImplementation), ServiceLifetime.Scoped);
        }

        /// <summary>
        /// 注册服务工厂
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="factory">服务工厂方法</param>
        public void RegisterFactory<TService>(Func<IContainer, TService> factory) where TService : class
        {
            if (factory == null)
                throw new ArgumentNullException(nameof(factory));

            _factories[typeof(TService)] = container => factory(container);
            OnServiceRegistered(typeof(TService), null, ServiceLifetime.Transient);
        }

        /// <summary>
        /// 注册带键的服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="key">服务键</param>
        /// <param name="instance">服务实例</param>
        public void RegisterKeyed<TService>(string key, TService instance) where TService : class
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("服务键不能为空", nameof(key));
            if (instance == null)
                throw new ArgumentNullException(nameof(instance));

            var fullKey = $"{typeof(TService).FullName}:{key}";
            _keyedServices[fullKey] = instance;

            OnServiceRegistered(typeof(TService), instance.GetType(), ServiceLifetime.Singleton, key);
        }

        /// <summary>
        /// 解析服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>服务实例</returns>
        public TService Resolve<TService>() where TService : class
        {
            var stopwatch = Stopwatch.StartNew();
            var fromCache = false;

            try
            {
                // 首先检查单例缓存
                if (_singletonCache.TryGetValue(typeof(TService), out var cachedInstance))
                {
                    fromCache = true;
                    var cached = (TService)cachedInstance;
                    _statistics.RecordResolution(stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    OnServiceResolved(typeof(TService), cached, stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    return cached;
                }

                // 检查工厂注册
                if (_factories.TryGetValue(typeof(TService), out var factory))
                {
                    var instance = (TService)factory(this);
                    _statistics.RecordResolution(stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    OnServiceResolved(typeof(TService), instance, stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    return instance;
                }

                // 使用服务注册器解析
                var service = _serviceRegistry.GetService<TService>();
                
                // 如果是单例，缓存起来
                if (_serviceRegistry.IsSingleton<TService>())
                {
                    _singletonCache.TryAdd(typeof(TService), service);
                }

                _statistics.RecordResolution(stopwatch.Elapsed.TotalMilliseconds, fromCache);
                OnServiceResolved(typeof(TService), service, stopwatch.Elapsed.TotalMilliseconds, fromCache);
                return service;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"无法解析服务 {typeof(TService).Name}", ex);
            }
        }

        /// <summary>
        /// 解析服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        public object Resolve(Type serviceType)
        {
            var stopwatch = Stopwatch.StartNew();
            var fromCache = false;

            try
            {
                // 首先检查单例缓存
                if (_singletonCache.TryGetValue(serviceType, out var cachedInstance))
                {
                    fromCache = true;
                    _statistics.RecordResolution(stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    OnServiceResolved(serviceType, cachedInstance, stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    return cachedInstance;
                }

                // 检查工厂注册
                if (_factories.TryGetValue(serviceType, out var factory))
                {
                    var instance = factory(this);
                    _statistics.RecordResolution(stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    OnServiceResolved(serviceType, instance, stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    return instance;
                }

                // 使用服务注册器解析
                var service = _serviceRegistry.GetService(serviceType);
                
                // 如果是单例，缓存起来
                if (_serviceRegistry.IsSingleton(serviceType))
                {
                    _singletonCache.TryAdd(serviceType, service);
                }

                _statistics.RecordResolution(stopwatch.Elapsed.TotalMilliseconds, fromCache);
                OnServiceResolved(serviceType, service, stopwatch.Elapsed.TotalMilliseconds, fromCache);
                return service;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"无法解析服务 {serviceType.Name}", ex);
            }
        }

        /// <summary>
        /// 尝试解析服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>服务实例，如果未找到则返回null</returns>
        public TService? TryResolve<TService>() where TService : class
        {
            try
            {
                return Resolve<TService>();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 尝试解析服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例，如果未找到则返回null</returns>
        public object? TryResolve(Type serviceType)
        {
            try
            {
                return Resolve(serviceType);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 解析带键的服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="key">服务键</param>
        /// <returns>服务实例</returns>
        public TService ResolveKeyed<TService>(string key) where TService : class
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("服务键不能为空", nameof(key));

            var fullKey = $"{typeof(TService).FullName}:{key}";
            if (_keyedServices.TryGetValue(fullKey, out var service))
            {
                return (TService)service;
            }

            throw new InvalidOperationException($"未找到键为 {key} 的服务 {typeof(TService).Name}");
        }

        /// <summary>
        /// 解析所有指定类型的服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>服务实例集合</returns>
        public IEnumerable<TService> ResolveAll<TService>() where TService : class
        {
            // 默认实现返回单个服务
            var service = TryResolve<TService>();
            return service != null ? new[] { service } : Enumerable.Empty<TService>();
        }

        /// <summary>
        /// 解析所有指定类型的服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例集合</returns>
        public IEnumerable<object> ResolveAll(Type serviceType)
        {
            // 默认实现返回单个服务
            var service = TryResolve(serviceType);
            return service != null ? new[] { service } : Enumerable.Empty<object>();
        }

        /// <summary>
        /// 检查服务是否已注册
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>是否已注册</returns>
        public bool IsRegistered<TService>() where TService : class
        {
            return IsRegistered(typeof(TService));
        }

        /// <summary>
        /// 检查服务是否已注册
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>是否已注册</returns>
        public bool IsRegistered(Type serviceType)
        {
            return _factories.ContainsKey(serviceType) || _serviceRegistry.IsRegistered(serviceType);
        }

        /// <summary>
        /// 检查带键的服务是否已注册
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="key">服务键</param>
        /// <returns>是否已注册</returns>
        public bool IsKeyedRegistered<TService>(string key) where TService : class
        {
            if (string.IsNullOrEmpty(key))
                return false;

            var fullKey = $"{typeof(TService).FullName}:{key}";
            return _keyedServices.ContainsKey(fullKey);
        }

        /// <summary>
        /// 创建子容器
        /// </summary>
        /// <returns>子容器实例</returns>
        public IContainer CreateChildContainer()
        {
            var childRegistry = new DefaultServiceRegistry();
            return new DefaultContainerAdapter(childRegistry);
        }

        /// <summary>
        /// 获取所有已注册的服务类型
        /// </summary>
        /// <returns>服务类型集合</returns>
        public IEnumerable<Type> GetRegisteredTypes()
        {
            var types = new HashSet<Type>();

            // 添加工厂注册的类型
            foreach (var type in _factories.Keys)
            {
                types.Add(type);
            }

            // 添加服务注册器中的类型
            foreach (var type in _serviceRegistry.GetRegisteredTypes())
            {
                types.Add(type);
            }

            return types;
        }

        /// <summary>
        /// 触发服务解析事件
        /// </summary>
        protected virtual void OnServiceResolved(Type serviceType, object serviceInstance, double resolutionTime, bool fromCache)
        {
            ServiceResolved?.Invoke(this, new ServiceResolvedEventArgs(serviceType, serviceInstance, resolutionTime, fromCache));
        }

        /// <summary>
        /// 触发服务注册事件
        /// </summary>
        protected virtual void OnServiceRegistered(Type serviceType, Type? implementationType, ServiceLifetime lifetime, string? key = null)
        {
            _statistics.IncrementRegistrations();
            ServiceRegistered?.Invoke(this, new ServiceRegisteredEventArgs(serviceType, implementationType, lifetime, key));
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _keyedServices.Clear();
                _factories.Clear();
                _singletonCache.Clear();
                _disposed = true;
            }
        }
    }
}
