using System;
using System.Collections.Generic;

namespace McLaser.Core.Framework.Container
{
    /// <summary>
    /// 统一容器接口
    /// 提供依赖注入容器的标准化操作
    /// </summary>
    public interface IContainer : IDisposable
    {
        /// <summary>
        /// 注册单例服务
        /// </summary>
        /// <typeparam name="TService">服务接口类型</typeparam>
        /// <typeparam name="TImplementation">服务实现类型</typeparam>
        void RegisterSingleton<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService;

        /// <summary>
        /// 注册单例服务实例
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="instance">服务实例</param>
        void RegisterSingleton<TService>(TService instance) where TService : class;

        /// <summary>
        /// 注册瞬态服务
        /// </summary>
        /// <typeparam name="TService">服务接口类型</typeparam>
        /// <typeparam name="TImplementation">服务实现类型</typeparam>
        void RegisterTransient<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService;

        /// <summary>
        /// 注册作用域服务
        /// </summary>
        /// <typeparam name="TService">服务接口类型</typeparam>
        /// <typeparam name="TImplementation">服务实现类型</typeparam>
        void RegisterScoped<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService;

        /// <summary>
        /// 注册服务工厂
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="factory">服务工厂方法</param>
        void RegisterFactory<TService>(Func<IContainer, TService> factory) where TService : class;

        /// <summary>
        /// 注册带键的服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="key">服务键</param>
        /// <param name="instance">服务实例</param>
        void RegisterKeyed<TService>(string key, TService instance) where TService : class;

        /// <summary>
        /// 解析服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>服务实例</returns>
        TService Resolve<TService>() where TService : class;

        /// <summary>
        /// 解析服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        object Resolve(Type serviceType);

        /// <summary>
        /// 尝试解析服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>服务实例，如果未找到则返回null</returns>
        TService? TryResolve<TService>() where TService : class;

        /// <summary>
        /// 尝试解析服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例，如果未找到则返回null</returns>
        object? TryResolve(Type serviceType);

        /// <summary>
        /// 解析带键的服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="key">服务键</param>
        /// <returns>服务实例</returns>
        TService ResolveKeyed<TService>(string key) where TService : class;

        /// <summary>
        /// 解析所有指定类型的服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>服务实例集合</returns>
        IEnumerable<TService> ResolveAll<TService>() where TService : class;

        /// <summary>
        /// 解析所有指定类型的服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例集合</returns>
        IEnumerable<object> ResolveAll(Type serviceType);

        /// <summary>
        /// 检查服务是否已注册
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>是否已注册</returns>
        bool IsRegistered<TService>() where TService : class;

        /// <summary>
        /// 检查服务是否已注册
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>是否已注册</returns>
        bool IsRegistered(Type serviceType);

        /// <summary>
        /// 检查带键的服务是否已注册
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="key">服务键</param>
        /// <returns>是否已注册</returns>
        bool IsKeyedRegistered<TService>(string key) where TService : class;

        /// <summary>
        /// 创建子容器
        /// </summary>
        /// <returns>子容器实例</returns>
        IContainer CreateChildContainer();

        /// <summary>
        /// 获取所有已注册的服务类型
        /// </summary>
        /// <returns>服务类型集合</returns>
        IEnumerable<Type> GetRegisteredTypes();

        /// <summary>
        /// 容器性能统计信息
        /// </summary>
        IContainerStatistics Statistics { get; }

        /// <summary>
        /// 服务解析事件
        /// </summary>
        event EventHandler<ServiceResolvedEventArgs>? ServiceResolved;

        /// <summary>
        /// 服务注册事件
        /// </summary>
        event EventHandler<ServiceRegisteredEventArgs>? ServiceRegistered;
    }

    /// <summary>
    /// 容器统计信息接口
    /// </summary>
    public interface IContainerStatistics
    {
        /// <summary>
        /// 总注册服务数量
        /// </summary>
        int TotalRegistrations { get; }

        /// <summary>
        /// 总解析次数
        /// </summary>
        long TotalResolutions { get; }

        /// <summary>
        /// 平均解析时间（毫秒）
        /// </summary>
        double AverageResolutionTime { get; }

        /// <summary>
        /// 缓存命中率
        /// </summary>
        double CacheHitRate { get; }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        void Reset();
    }

    /// <summary>
    /// 服务解析事件参数
    /// </summary>
    public class ServiceResolvedEventArgs : EventArgs
    {
        /// <summary>
        /// 服务类型
        /// </summary>
        public Type ServiceType { get; }

        /// <summary>
        /// 服务实例
        /// </summary>
        public object ServiceInstance { get; }

        /// <summary>
        /// 解析耗时（毫秒）
        /// </summary>
        public double ResolutionTime { get; }

        /// <summary>
        /// 是否来自缓存
        /// </summary>
        public bool FromCache { get; }

        public ServiceResolvedEventArgs(Type serviceType, object serviceInstance, double resolutionTime, bool fromCache)
        {
            ServiceType = serviceType;
            ServiceInstance = serviceInstance;
            ResolutionTime = resolutionTime;
            FromCache = fromCache;
        }
    }

    /// <summary>
    /// 服务注册事件参数
    /// </summary>
    public class ServiceRegisteredEventArgs : EventArgs
    {
        /// <summary>
        /// 服务类型
        /// </summary>
        public Type ServiceType { get; }

        /// <summary>
        /// 实现类型
        /// </summary>
        public Type? ImplementationType { get; }

        /// <summary>
        /// 服务生命周期
        /// </summary>
        public ServiceLifetime Lifetime { get; }

        /// <summary>
        /// 服务键
        /// </summary>
        public string? Key { get; }

        public ServiceRegisteredEventArgs(Type serviceType, Type? implementationType, ServiceLifetime lifetime, string? key = null)
        {
            ServiceType = serviceType;
            ImplementationType = implementationType;
            Lifetime = lifetime;
            Key = key;
        }
    }

    /// <summary>
    /// 服务生命周期枚举
    /// </summary>
    public enum ServiceLifetime
    {
        /// <summary>
        /// 瞬态：每次解析都创建新实例
        /// </summary>
        Transient,

        /// <summary>
        /// 单例：整个容器生命周期内只有一个实例
        /// </summary>
        Singleton,

        /// <summary>
        /// 作用域：在同一作用域内是单例
        /// </summary>
        Scoped
    }
}
