using System;
using McLaser.Core.Framework.Container;

namespace McLaser.Core
{
    /// <summary>
    /// 测试程序
    /// 用于验证统一DI容器的功能
    /// </summary>
    public class TestProgram
    {
        /// <summary>
        /// 运行测试
        /// </summary>
        public static void RunTests()
        {
            Console.WriteLine("=== McLaser.Core 统一DI容器测试 ===");
            Console.WriteLine();

            // 运行基本测试
            Console.WriteLine("1. 运行基本容器测试...");
            var basicResult = ContainerTests.RunBasicTests();
            PrintTestResult(basicResult);

            // 重置容器
            ContainerManager.Reset();

            // 运行MEF测试
            Console.WriteLine("2. 运行MEF容器测试...");
            var mefResult = ContainerTests.RunMefTests();
            PrintTestResult(mefResult);

            // 重置容器
            ContainerManager.Reset();

            // 运行性能测试
            Console.WriteLine("3. 运行性能测试...");
            ContainerManager.UseDefaultContainer();
            var perfResult = ContainerTests.RunPerformanceTests();
            PrintTestResult(perfResult);

            // 显示容器统计信息
            Console.WriteLine("4. 容器统计信息:");
            var stats = ContainerManager.GetStatistics();
            Console.WriteLine($"   总注册数: {stats.TotalRegistrations}");
            Console.WriteLine($"   总解析数: {stats.TotalResolutions}");
            Console.WriteLine($"   平均解析时间: {stats.AverageResolutionTime:F2}ms");
            Console.WriteLine($"   缓存命中率: {stats.CacheHitRate:P2}");

            // 验证容器配置
            Console.WriteLine("5. 验证容器配置:");
            var validation = ContainerManager.ValidateConfiguration();
            Console.WriteLine($"   {validation.GetSummary()}");
            
            if (!validation.IsValid)
            {
                foreach (var error in validation.Errors)
                {
                    Console.WriteLine($"   错误: {error}");
                }
            }

            Console.WriteLine();
            Console.WriteLine("=== 测试完成 ===");
        }

        /// <summary>
        /// 打印测试结果
        /// </summary>
        /// <param name="result">测试结果</param>
        private static void PrintTestResult(ContainerTestResult result)
        {
            Console.WriteLine($"   {result.GetSummary()}");
            
            if (result.PassedTests.Count > 0)
            {
                Console.WriteLine("   通过的测试:");
                foreach (var test in result.PassedTests)
                {
                    Console.WriteLine($"     ✓ {test}");
                }
            }

            if (result.FailedTests.Count > 0)
            {
                Console.WriteLine("   失败的测试:");
                foreach (var test in result.FailedTests)
                {
                    Console.WriteLine($"     ✗ {test}");
                }
            }

            if (result.Errors.Count > 0)
            {
                Console.WriteLine("   错误:");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"     ! {error}");
                }
            }

            if (result.Info.Count > 0)
            {
                Console.WriteLine("   信息:");
                foreach (var info in result.Info)
                {
                    Console.WriteLine($"     i {info}");
                }
            }

            Console.WriteLine();
        }
    }
}
